#include "key.h"
#include "nrf_drv_gpiote.h"
#include "gui_guider.h"
#include "main.h"
#include "analog.h"
#include "sm.h"

//#include "nrf_pwr_mgmt.h"


//按键状态机
typedef enum {
    KEY_STATE_RELEASED,         //确定松开
    KEY_STATE_MAY_PRESSED,      //可能被按下
    KEY_STATE_PRESSED,          //确认按下
    KEY_STATE_MAY_RELEASED,     //可能松开
} key_state_t;



APP_TIMER_DEF(key_user_timer_id);
APP_TIMER_DEF(key_press_timer_id);
APP_TIMER_DEF(key_up_timer_id);
APP_TIMER_DEF(key_down_timer_id);

// 双击检测定时器（只有用户键支持双击）
APP_TIMER_DEF(key_user_double_click_timer_id);

//函数声明
void set_key_index(ui_evt_t *ui_evt,uint32_t index);

static nrfx_gpiote_pin_t m_key_pin[4] = {KEY_USER_PIN, KEY_PRESS_PIN,KEY_UP_PIN,KEY_DOWN_PIN};  //按键PIN
static app_timer_id_t m_timer_id[4];            //定时器ID
static app_timer_id_t m_double_click_timer_id[4]; //双击检测定时器ID
static uint32_t m_is_key_timer_runing[4] = {0}; //定时器是否在运行标志
static uint32_t m_is_double_click_timer_runing[4] = {0}; //双击定时器是否在运行标志
static key_state_t m_key_state[4];              //按键状态机
static uint32_t m_key_pressed_ms[4] = {0};   //长按计时
static uint8_t key_long_press_sent[4] = {0}; // 用于长按触发时不触发短按

// 双击检测相关变量
static uint32_t m_first_click_time[4] = {0};     // 第一次点击的时间戳
static uint8_t m_waiting_for_second_click[4] = {0}; // 是否在等待第二次点击
static uint8_t m_double_click_sent[4] = {0};     // 双击事件是否已发送
static uint32_t m_first_click_duration[4] = {0}; // 第一次点击的持续时间

bool user_key_long_press_handled = false;			//用户按键长按标志位（只响应长按）
bool press_key_long_press_handled = false;		//探头按键长按标志位（只响应长按）

//自动切回主界面倒计时
extern uint32_t start_up_ms;

// 按键状态恢复机制
static uint32_t key_recovery_counter = 0;
static bool key_system_blocked = false;

// 按键全局禁用标志
static bool key_global_enabled = true;

//主界面（结果界面）标志位 默认umol
ui_screen_t main_screen = UI_SCREEN_RESULT1;


//开始定时器
static void key_timer_start(uint32_t index)
{
    ret_code_t err_code;

    if (m_is_key_timer_runing[index] == 0) {
        err_code = app_timer_start(m_timer_id[index], APP_TIMER_TICKS(KEY_ANTI_SHAKE_MS), NULL);
        APP_ERROR_CHECK(err_code);
        m_is_key_timer_runing[index] = 1;
    }
}
//停止定时器
static void key_timer_stop(uint32_t index)
{
    ret_code_t err_code;

    if (m_is_key_timer_runing[index] != 0) {
        err_code = app_timer_stop(m_timer_id[index]);
        APP_ERROR_CHECK(err_code);
        m_is_key_timer_runing[index] = 0;
    }
}

//重启定时器
static void key_timer_restart(uint32_t index)
{
    ret_code_t err_code;

    err_code = app_timer_stop(m_timer_id[index]);
    APP_ERROR_CHECK(err_code);
    err_code = app_timer_start(m_timer_id[index], APP_TIMER_TICKS(KEY_ANTI_SHAKE_MS), NULL);
    APP_ERROR_CHECK(err_code);
    m_is_key_timer_runing[index] = 1;
}

//开始双击定时器（只有用户键支持）
static void double_click_timer_start(uint32_t index)
{
    ret_code_t err_code;

    // 只有用户键（index=0）支持双击检测
    if (index == 0 && m_is_double_click_timer_runing[index] == 0) {
        err_code = app_timer_start(m_double_click_timer_id[index], APP_TIMER_TICKS(KEY_DOUBLE_CLICK_WINDOW_MS), NULL);
        APP_ERROR_CHECK(err_code);
        m_is_double_click_timer_runing[index] = 1;
    }
}

//停止双击定时器（只有用户键支持）
static void double_click_timer_stop(uint32_t index)
{
    ret_code_t err_code;

    // 只有用户键（index=0）支持双击检测
    if (index == 0 && m_is_double_click_timer_runing[index] != 0) {
        err_code = app_timer_stop(m_double_click_timer_id[index]);
        APP_ERROR_CHECK(err_code);
        m_is_double_click_timer_runing[index] = 0;
    }
}

//按键中断处理
static void key_press_handler_handle(uint32_t index)
{
    uint32_t pin_value;

    //获取当前电平
    pin_value = nrf_gpio_pin_read(m_key_pin[index]);

    if (m_key_state[index] == KEY_STATE_RELEASED) { //确定松开状态
        if (pin_value == 0) {
            m_key_state[index] = KEY_STATE_MAY_PRESSED;//可能按下
            //开始定时器，一段时间后判断再确认状态
            key_timer_start(index);
        }
    } else if (m_key_state[index] == KEY_STATE_PRESSED) { //确定按下状态
        if (pin_value == 1) {
            m_key_state[index] = KEY_STATE_MAY_RELEASED;//可能松开
            // 立即发出释放信号
            if (key_global_enabled) {
                ui_evt_t ui_evt;
                ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                set_key_index(&ui_evt, index);
                ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_RELEASED;
                ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
//                NRF_LOG_INFO("key%d released", index);
                key_system_reset_recovery(); // 重置恢复计数器
                sm_event(ui_evt);
            } else {
                NRF_LOG_INFO("Key disabled - ignoring key%d released event", index);
            }

            //重启定时器，一段时间后再确认状态
            key_timer_restart(index);
        }
    }
}

//设置索引所对应的按键
void set_key_index(ui_evt_t *ui_evt,uint32_t index)
{
    switch(index) {
    case 0:
        ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_USER;
        break;
    case 1:
        ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_PRESS;
        break;
    case 2:
        ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_UP;
        break;
    case 3:
        ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_DOWN;
        break;
    }
}

//定时器回调
static void key_user_timeout_handler_handle(uint32_t index)
{
    uint32_t pin_value;

    //获取当前电平
    pin_value = nrf_gpio_pin_read(m_key_pin[index]);
    if (m_key_state[index] == KEY_STATE_MAY_PRESSED) { //可能按下状态
        if (pin_value == 0) {
            m_key_state[index] = KEY_STATE_PRESSED;//确定按下
            m_key_pressed_ms[index] = 0;
            key_long_press_sent[index] = 0; // 按下时清除长按标志

            // 如果正在等待第二次点击，这是第二次按下，不要取消双击检测
            // 双击检测将在释放时进行判断
            if (m_waiting_for_second_click[index]) {
//                NRF_LOG_INFO("key%d second press detected, continuing double click detection", index);
            }

            // 上报按下事件 KEY_EVENT_TYPE_DOWN
            if (key_global_enabled) {
                ui_evt_t ui_evt;
                ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                set_key_index(&ui_evt, index);
                ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_DOWN;
                ui_evt.evt.ui_evt_key.key_pressed_ms = 0;
                sm_event(ui_evt);
            } else {
                NRF_LOG_INFO("Key disabled - ignoring key%d down event", index);
            }

            //重启定时器，使得长按的事件时间更精确
            key_timer_restart(index);
            //重载关机定时器
            system_auto_power_off_timer_reload();
        } else { //误检，没有按下
            m_key_state[index] = KEY_STATE_RELEASED;//回到确定松开状态
            //停止定时器
            key_timer_stop(index);
        }
    } else if (m_key_state[index] == KEY_STATE_MAY_RELEASED) { //可能松开状态
        if (pin_value == 1) {
            m_key_state[index] = KEY_STATE_RELEASED;//确定松开
            key_timer_stop(index);

            // 只有未发生长按时才处理短按或双击逻辑
            if (!key_long_press_sent[index]) {
                if (key_global_enabled) {
                    // 双击检测逻辑（只有用户键支持）
                    if (m_waiting_for_second_click[index] && index == 0) {
                        // 验证时间间隔
                        uint32_t current_time = app_timer_cnt_get();
                        uint32_t time_diff = app_timer_cnt_diff_compute(current_time, m_first_click_time[index]);
                        uint32_t time_diff_ms = (time_diff * 1000) / APP_TIMER_CLOCK_FREQ;

                        NRF_LOG_INFO("key%d second click detected, time diff: %d ms, duration: %d ms",
                                     index, time_diff_ms, m_key_pressed_ms[index]);

                        // 确保第二次点击也是短按（不是长按）
                        if (m_key_pressed_ms[index] < KEY_LONG_PRESS_START_MS) {
                            // 这是第二次短按，触发双击事件
                            m_waiting_for_second_click[index] = 0;
                            m_double_click_sent[index] = 0; // 重置双击发送标志，为下次检测做准备
                            m_first_click_time[index] = 0;
                            m_first_click_duration[index] = 0; // 重置第一次点击持续时间
                            double_click_timer_stop(index);

                            ui_evt_t ui_evt;
                            ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                            set_key_index(&ui_evt, index);
                            ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_DOUBLE_CLICK;
                            ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
                            NRF_LOG_INFO("key%d double clicked", index);
                            sm_event(ui_evt);
                        } else {
                            // 第二次点击是长按，取消双击检测，发送第一次的短按事件
                            NRF_LOG_INFO("key%d second click was long press, canceling double click", index);
                            m_waiting_for_second_click[index] = 0;
                            m_double_click_sent[index] = 0; // 重置双击发送标志
                            m_first_click_time[index] = 0;
                            m_first_click_duration[index] = 0; // 重置第一次点击持续时间
                            double_click_timer_stop(index);

                            // 发送第一次点击的短按事件
                            ui_evt_t ui_evt;
                            ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                            set_key_index(&ui_evt, index);
                            ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_PRESSED;
                            ui_evt.evt.ui_evt_key.key_pressed_ms = m_first_click_duration[index];
                            NRF_LOG_INFO("key%d sending delayed single press", index);
                            sm_event(ui_evt);
                        }
                    } else {
                        // 检查这是否是短按（不是长按）
                        if (m_key_pressed_ms[index] < KEY_LONG_PRESS_START_MS) {
                            // 只有用户键（index=0）支持双击检测
                            if (index == 0) {
                                // 用户键：启动双击检测，延迟发送短按事件
                                m_waiting_for_second_click[index] = 1;
                                m_double_click_sent[index] = 0;
                                m_first_click_time[index] = app_timer_cnt_get(); // 记录第一次点击的时间戳
                                m_first_click_duration[index] = m_key_pressed_ms[index]; // 保存第一次点击的持续时间
                                double_click_timer_start(index);
                                NRF_LOG_INFO("key%d first short click (duration: %d ms), waiting for second click",
                                             index, m_key_pressed_ms[index]);
                                // 注意：不立即发送短按事件，等待双击检测窗口结束
                            } else {
                                // 其他按键（探头键、上键、下键）：立即发送短按事件，不支持双击
                                ui_evt_t ui_evt;
                                ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                                set_key_index(&ui_evt, index);
                                ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_PRESSED;
                                ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
                                NRF_LOG_INFO("key%d single pressed (immediate, no double click support)", index);
                                sm_event(ui_evt);
                            }
                        } else {
                            // 这是长按，直接发送短按事件（实际上这种情况不应该发生，因为长按已经被处理了）
                            NRF_LOG_INFO("key%d first click was long press, sending single press immediately", index);
                            ui_evt_t ui_evt;
                            ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                            set_key_index(&ui_evt, index);
                            ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_PRESSED;
                            ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
                            sm_event(ui_evt);
                        }
                    }
                } else {
                    NRF_LOG_INFO("Key disabled - ignoring key%d pressed event", index);
                }
            }
            key_long_press_sent[index] = 0; // 松开时清除长按标志
            start_up_ms = 0;
        } else { //误检，没有松开
            m_key_state[index] = KEY_STATE_PRESSED;//回到按下状态
        }
    } else if (m_key_state[index] == KEY_STATE_PRESSED) { //确定按下状态
        //长按检测
        m_key_pressed_ms[index] += KEY_ANTI_SHAKE_MS;
        if ((m_key_pressed_ms[index] >= KEY_LONG_PRESS_START_MS)
            && (m_key_pressed_ms[index] % KEY_LONG_PRESS_INTERVAL_MS == 0)) {
            if (!key_long_press_sent[index]) {
                // 如果正在等待第二次点击，取消双击检测
                if (m_waiting_for_second_click[index]) {
                    NRF_LOG_INFO("key%d canceling double click detection due to long press", index);
                    m_waiting_for_second_click[index] = 0;
                    m_double_click_sent[index] = 0;
                    m_first_click_time[index] = 0;
                    m_first_click_duration[index] = 0; // 重置第一次点击持续时间
                    double_click_timer_stop(index);

                    // 发送第一次点击的短按事件
                    if (key_global_enabled) {
                        ui_evt_t ui_evt;
                        ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                        set_key_index(&ui_evt, index);
                        ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_PRESSED;
                        ui_evt.evt.ui_evt_key.key_pressed_ms = m_first_click_duration[index];
                        NRF_LOG_INFO("key%d sending delayed single press due to long press", index);
                        sm_event(ui_evt);
                    }
                }

                //上报事件：长按
                if (key_global_enabled) {
//                    NRF_LOG_INFO("key%d long pressed", index);
                    ui_evt_t ui_evt;
                    ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                    set_key_index(&ui_evt, index);
                    ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_LONG_PRESS;
                    ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
                    sm_event(ui_evt);
                } else {
                    NRF_LOG_INFO("Key disabled - ignoring key%d long press event", index);
                }
                key_long_press_sent[index] = 1; // 标记已长按
            }
        }
    }
}



//user按键中断
static void key_user_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(0);
}


//press按键中断
static void key_press_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(1);
}

//up按键中断
static void key_up_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(2);
}

//down按键中断
static void key_down_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(3);
}

//user定时器回调
static void key_user_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(0);
}


//press定时器回调
static void key_press_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(1);
}

//up定时器回调
static void key_up_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(2);
}

//down定时器回调
static void key_down_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(3);
}

// 双击定时器回调处理函数
static void double_click_timeout_handler_handle(uint32_t index)
{
    // 双击窗口超时，发送第一次的短按事件（只有用户键需要）
    if (m_waiting_for_second_click[index] && !m_double_click_sent[index] && index == 0) {
        NRF_LOG_INFO("key%d double click timeout, sending delayed single press", index);
        m_waiting_for_second_click[index] = 0;
        m_double_click_sent[index] = 0; // 重置双击发送标志
        m_first_click_time[index] = 0;
        m_first_click_duration[index] = 0; // 重置第一次点击持续时间
        double_click_timer_stop(index);

        // 发送第一次点击的短按事件
        if (key_global_enabled) {
            ui_evt_t ui_evt;
            ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
            set_key_index(&ui_evt, index);
            ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_PRESSED;
            ui_evt.evt.ui_evt_key.key_pressed_ms = m_first_click_duration[index];
            NRF_LOG_INFO("key%d single pressed (after double click timeout)", index);
            sm_event(ui_evt);
        }
    }
}

//user双击定时器回调
static void key_user_double_click_timeout_handler(void * p_context)
{
    double_click_timeout_handler_handle(0);
}



//获取实时按键状态
bool is_key_pressed(key_instance_t key_instance)
{
    if (m_key_state[key_instance] == KEY_STATE_PRESSED) {
        return true;
    } else {
        return false;
    }
}
//按键初始化
void key_init(void)
{
    ret_code_t err_code;

    // 初始化时按键默认启用（但会在setup_ui中被禁用）
    key_global_enabled = true;

    m_timer_id[0] = key_user_timer_id;
    m_timer_id[1] = key_press_timer_id;
    m_timer_id[2] = key_up_timer_id;
    m_timer_id[3] = key_down_timer_id;

    // 双击定时器ID初始化（只有用户键支持双击）
    m_double_click_timer_id[0] = key_user_double_click_timer_id;

    //中断
    nrf_drv_gpiote_in_config_t gpiote_config = GPIOTE_CONFIG_IN_SENSE_TOGGLE(true);
    gpiote_config.pull = NRF_GPIO_PIN_PULLUP;

    err_code = nrf_drv_gpiote_in_init(KEY_USER_PIN, &gpiote_config, key_user_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_USER_PIN, true);

    err_code = nrf_drv_gpiote_in_init(KEY_PRESS_PIN, &gpiote_config, key_press_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_PRESS_PIN, true);

    err_code = nrf_drv_gpiote_in_init(KEY_UP_PIN, &gpiote_config, key_up_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_UP_PIN, true);

    err_code = nrf_drv_gpiote_in_init(KEY_DOWN_PIN, &gpiote_config, key_down_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_DOWN_PIN, true);

    //定时器
    err_code = app_timer_create(&key_user_timer_id, APP_TIMER_MODE_REPEATED, key_user_timeout_handler);
    APP_ERROR_CHECK(err_code);

    err_code = app_timer_create(&key_press_timer_id, APP_TIMER_MODE_REPEATED, key_press_timeout_handler);
    APP_ERROR_CHECK(err_code);

    err_code = app_timer_create(&key_up_timer_id, APP_TIMER_MODE_REPEATED, key_up_timeout_handler);
    APP_ERROR_CHECK(err_code);

    err_code = app_timer_create(&key_down_timer_id, APP_TIMER_MODE_REPEATED, key_down_timeout_handler);
    APP_ERROR_CHECK(err_code);

    // 双击定时器创建（只有用户键支持双击）
    err_code = app_timer_create(&key_user_double_click_timer_id, APP_TIMER_MODE_SINGLE_SHOT, key_user_double_click_timeout_handler);
    APP_ERROR_CHECK(err_code);

    //初始状态
    for (uint32_t i = 0; i < 4; i++) {
        m_key_state[i] = KEY_STATE_RELEASED;
        m_is_key_timer_runing[i] = 0;
        m_is_double_click_timer_runing[i] = 0;
        m_waiting_for_second_click[i] = 0;
        m_double_click_sent[i] = 0;
        m_first_click_duration[i] = 0;
        m_first_click_time[i] = 0;
    }
}


//按键反初始化
void key_deinit(void)
{
    nrf_gpio_cfg_input(KEY_PRESS_PIN, NRF_GPIO_PIN_NOPULL);
}

// 按键系统恢复函数
void key_system_recovery(void)
{
    key_recovery_counter++;

    // 如果检测到按键系统可能被阻塞（超过5秒没有按键响应）
    if (key_recovery_counter > 5000) {
        key_system_blocked = true;

        // 重置所有按键状态
        for (int i = 0; i < 4; i++) {
            m_key_state[i] = KEY_STATE_RELEASED;
            m_key_pressed_ms[i] = 0;
            key_long_press_sent[i] = 0;
            m_waiting_for_second_click[i] = 0;
            m_double_click_sent[i] = 0;
            m_first_click_duration[i] = 0;
            m_first_click_time[i] = 0;

            // 停止所有按键定时器
            if (m_is_key_timer_runing[i]) {
                app_timer_stop(m_timer_id[i]);
                m_is_key_timer_runing[i] = 0;
            }

            // 停止双击定时器（只有用户键支持）
            if (i == 0 && m_is_double_click_timer_runing[i]) {
                app_timer_stop(m_double_click_timer_id[i]);
                m_is_double_click_timer_runing[i] = 0;
            }
        }

        key_recovery_counter = 0;
        key_system_blocked = false;

    }
}

// 重置按键恢复计数器（在按键事件发生时调用）
void key_system_reset_recovery(void)
{
    key_recovery_counter = 0;
    key_system_blocked = false;
}

// 禁用所有按键
void key_disable_all(void)
{
    key_global_enabled = false;
    NRF_LOG_INFO("All keys disabled");
}

// 启用所有按键
void key_enable_all(void)
{
    key_global_enabled = true;
    NRF_LOG_INFO("All keys enabled");
}

// 检查按键是否启用
bool key_is_enabled(void)
{
    return key_global_enabled;
}

 // 长按开机检测函数
void wait_user_key_long_press(void)
 {
     // 配置USER按键为输入，上拉
     nrf_gpio_cfg_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP);

     uint32_t press_count = 0;
     const uint32_t required_press_time = 3000; // 3秒 = 3000ms
     const uint32_t check_interval = 10;        // 每10ms检查一次
     const uint32_t required_count = required_press_time / check_interval; // 300次
     const uint32_t timeout_count = 30000 / check_interval; // 30秒超时
     uint32_t total_count = 0;

     while (1) {
         total_count++;

         // 超时保护：30秒内没有成功长按则进入system_off
         if (total_count >= timeout_count) {
             // 配置按键唤醒并进入system_off
             nrf_gpio_cfg_sense_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
             nrf_gpio_cfg_sense_input(LGS4056_CHRG_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
             sd_power_system_off();
         }

         // 检查按键状态 (按下为低电平)
         if (!nrf_gpio_pin_read(KEY_USER_PIN)) {
             press_count++;

             // 达到3秒，退出等待，继续启动
             if (press_count >= required_count) {
                 break;
             }
         } else {
             // 按键松开，重置计数
             press_count = 0;
         }

         // 延时10ms
         nrf_delay_ms(check_interval);
     }
 }