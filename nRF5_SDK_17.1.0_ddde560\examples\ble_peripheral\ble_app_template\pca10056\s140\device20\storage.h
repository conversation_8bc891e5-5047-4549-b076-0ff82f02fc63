#ifndef __STORAGE_H__
#define __STORAGE_H__


#include "main.h"
#include "fds.h"
#include "spiflash20.h"

//温度校准相关
typedef struct {
    uint32_t last_time;
    uint32_t last_temp;
    uint32_t	max_tempture;
} temprecord;


//校准参数
typedef struct {
    uint32_t is_valid;//0表示未校准，1表示已经校准，但不知道是什么校准方式，2表示普通校准，3表示特殊校准

    //全反射时（白板）
    uint32_t d0;
    uint32_t g0;
    uint32_t b0;
    //无反射时（对空气）
    uint32_t d1;
    uint32_t g1;
    uint32_t b1;
    uint8_t g_pwm;  //divid G灯亮时 ADC值为1000左右时的PWM值，经ADC三倍放大
    uint8_t b_pwm;  //divid B灯亮时 ADC值为3000左右时的PWM值,未经放大
    uint8_t temph;
    uint8_t templ;
    //结果调整
    uint8_t result_ratio;//100是1倍，90是0.9倍
    uint8_t dumy0;// 预留，字节对齐
    uint8_t dumy1;//
    uint8_t dumy2;//

    uint32_t gotkb;	  //divid   表示k b是否存在，值为0x55则表示存在
    uint32_t ksign;   //divid   k的正负值 正为1，负值为0
    uint32_t k;       //divid   k的真实值X10000
    uint32_t bsign;	  //divid   b的正负值 正为1，负值为0
    uint32_t b;       //divid   b的真实值X10000
    uint32_t stamp;		//divid   时间戳
    uint32_t uint;    //divid   当前单位
} cali_para_t;

//产测记录
typedef struct {
    uint32_t is_test_pass;
} factory_test_t;

typedef struct {
    meas_record_t meas_record[MEAS_RECORD_NUM];
    cali_para_t cali_para;
    factory_test_t factory_test;
    uint32_t is_locked;//
    uint8_t sn_str[20];
} store_t;


void fds_new(uint16_t file, uint16_t key, uint8_t* data, uint32_t len);
void storage_init(void);
void storage_read_record(uint32_t index, meas_record_t *meas_record);
void storage_update_record(meas_record_t *meas_record);
void storage_read_cali_para(cali_para_t *cali_para);
void storage_update_cali_para(cali_para_t *cali_para);
void storage_read_factory_test(factory_test_t *factory_test);
void storage_update_factory_test(factory_test_t *factory_test);
void storage_read_lock(uint32_t *is_lock);
void storage_update_lock(uint32_t *is_locked);
void storage_read_sn_str(uint8_t *str);
void storage_update_sn_str(uint8_t *str);

//divid add
void storage_read_kb_status(uint32_t *kb_status);
//更新是否得到k b的状态  0x55为已获取，其他值为未获取
void storage_update_kb_status(uint32_t *kb_status);
//获取K值的符号
void storage_read_k_sign(uint32_t *k);
//更新k值的符号
void storage_update_k_sign(uint32_t *k);
//获取b值的符号
void storage_read_b_sign(uint32_t *k);
//更新b值的符号
void storage_update_b_sign(uint32_t *k);

//获取k值 真实k值乘以10000，然后取整
void storage_read_k(uint32_t *k);
//更新k值
void storage_update_k(uint32_t *k);

//获取b值 真实b值乘以10000，然后取整
void storage_read_b(uint32_t *b);

//更新b值
void storage_update_b(uint32_t *b);
void storage_clear_kbsign(void);

void storage_read_stamp(uint32_t *stamp);
void storage_update_stamp(uint32_t stamp);


void storage_read_unit(uint32_t *uint);
void storage_updata_unit(uint32_t *uint);

void storage_update_duty(uint32_t gpwm,uint32_t bpwm);
void storage_read_duty(uint32_t *gduty,uint32_t *bduty);
void storage_update_tempture(uint8_t th,uint8_t tl);
void storage_read_tempture(uint8_t *th,uint8_t *tl);
//--------------------------------------------------------------


//--------------------------

void record_init(void);
void record_add_meas_record(meas_record_t *meas_record);
void record_send_all_history(void);
void record_save(uint32_t dark, uint32_t green, uint32_t blue, uint32_t bilirubin);
void delete_all_begin(void);
void delete_all_process(void);

void get_temp_data(temprecord *ptemp_data);
void set_temp_data(temprecord *ptemp_data);
void get_temp_flash_data(void);
void set_temp_flash_data(void);

#endif
