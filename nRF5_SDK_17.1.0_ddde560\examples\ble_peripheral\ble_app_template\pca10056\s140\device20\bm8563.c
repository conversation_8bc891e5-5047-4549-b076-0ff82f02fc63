#include "bm8563.h"
#include "nrf_gpio.h"
#include "app_error.h"
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdint.h>

//debug
#include "app_timer.h"
#include "nrf_log.h"

static Tm T;

//------------------------ 月天数表（支持闰年） ------------------------
const unsigned monDays[2][12] = {
    {31,28,31,30,31,30,31,31,30,31,30,31},
    {31,29,31,30,31,30,31,31,30,31,30,31}
};

//------------------------ 寄存器地址定义 ------------------------
const uint8_t BM8563_CTR_STATUS_ADDR_1 = 0x00;
const uint8_t BM8563_CTR_STATUS_ADDR_2 = 0x01;
const uint8_t BM8563_DATA_START_ADDR   = 0x02;
const uint8_t BM8563_CLOCKOUT          = 0x0D;

//------------------------ 读写数据缓存 ------------------------
uint8_t trdata[7] = {0}; 
uint8_t twdata[8] = {BM8563_DATA_START_ADDR,0x50,0x59,0x23,0x31,0x06,0x12,0x04};

//------------------------ 软件 I2C 基础操作 ------------------------

/**
 * @brief 延时函数，用于产生 I2C 时序所需的间隔
 */
static void I2C_Delay(void)
{
    for (volatile int i = 0; i < 10; i++) __NOP();
}

/**
 * @brief 将 SDA 引脚设置为输出
 */
static void SDA_OUT(void) { nrf_gpio_cfg_output(BM_SDA_PIN); }

/**
 * @brief 将 SDA 引脚设置为输入（用于读取应答信号或接收数据）
 */
static void SDA_IN(void)  { nrf_gpio_cfg_input(BM_SDA_PIN, NRF_GPIO_PIN_PULLUP); }

/**
 * @brief I2C 起始信号：SDA 先下降，SCL 后下降
 */
static void I2C_Start(void)
{
    SDA_OUT();
    nrf_gpio_pin_set(BM_SDA_PIN);
    nrf_gpio_pin_set(BM_SCL_PIN);
    I2C_Delay();
    nrf_gpio_pin_clear(BM_SDA_PIN);
    I2C_Delay();
    nrf_gpio_pin_clear(BM_SCL_PIN);
}

/**
 * @brief I2C 停止信号：SCL 先升，SDA 后升
 */
static void I2C_Stop(void)
{
    SDA_OUT();
    nrf_gpio_pin_clear(BM_SDA_PIN);
    nrf_gpio_pin_set(BM_SCL_PIN);
    I2C_Delay();
    nrf_gpio_pin_set(BM_SDA_PIN);
    I2C_Delay();
}

/**
 * @brief 等待应答信号（ACK）
 * @retval true 表示收到 ACK，false 表示未收到
 */
static bool I2C_WaitAck(void)
{
    bool ack;
    SDA_IN();
    I2C_Delay();
    nrf_gpio_pin_set(BM_SCL_PIN);
    I2C_Delay();
    ack = nrf_gpio_pin_read(BM_SDA_PIN);
    nrf_gpio_pin_clear(BM_SCL_PIN);
    SDA_OUT();
    return !ack;
}

/**
 * @brief 发送 ACK 信号
 */
static void I2C_Ack(void)
{
    SDA_OUT();
    nrf_gpio_pin_clear(BM_SDA_PIN);
    I2C_Delay();
    nrf_gpio_pin_set(BM_SCL_PIN);
    I2C_Delay();
    nrf_gpio_pin_clear(BM_SCL_PIN);
}

/**
 * @brief 发送 NACK 信号
 */
static void I2C_NAck(void)
{
    SDA_OUT();
    nrf_gpio_pin_set(BM_SDA_PIN);
    I2C_Delay();
    nrf_gpio_pin_set(BM_SCL_PIN);
    I2C_Delay();
    nrf_gpio_pin_clear(BM_SCL_PIN);
}

/**
 * @brief 向 I2C 总线发送一个字节
 * @param byte 要发送的数据
 */
static void I2C_SendByte(uint8_t byte)
{
    SDA_OUT();
    for (int i = 0; i < 8; i++) {
        (byte & 0x80) ? nrf_gpio_pin_set(BM_SDA_PIN) : nrf_gpio_pin_clear(BM_SDA_PIN);
        byte <<= 1;
        nrf_gpio_pin_set(BM_SCL_PIN);
        I2C_Delay();
        nrf_gpio_pin_clear(BM_SCL_PIN);
        I2C_Delay();
    }
}

/**
 * @brief 从 I2C 总线读取一个字节
 * @param ack 是否发送应答（true=ACK，false=NACK）
 * @return 读取的字节数据
 */
static uint8_t I2C_ReadByte(bool ack)
{
    uint8_t byte = 0;
    SDA_IN();
    for (int i = 0; i < 8; i++) {
        nrf_gpio_pin_set(BM_SCL_PIN);
        I2C_Delay();
        byte <<= 1;
        if (nrf_gpio_pin_read(BM_SDA_PIN)) byte++;
        nrf_gpio_pin_clear(BM_SCL_PIN);
        I2C_Delay();
    }
    if (ack)
        I2C_Ack();
    else
        I2C_NAck();
    return byte;
}

/**
 * @brief 初始化软件 I2C 引脚（SCL、SDA）
 */
void Soft_I2C_Init(void)
{
    nrf_gpio_cfg_output(BM_SCL_PIN);
    nrf_gpio_cfg_output(BM_SDA_PIN);
    nrf_gpio_pin_set(BM_SCL_PIN);
    nrf_gpio_pin_set(BM_SDA_PIN);
}

/**
 * @brief 软件 I2C 写函数（连续写入）
 * @param addr I2C 设备地址
 * @param data 写入数据缓冲区
 * @param len 写入长度
 * @return true 表示写入成功
 */
bool Soft_I2C_Write(uint8_t addr, uint8_t *data, uint8_t len)
{
    I2C_Start();
    I2C_SendByte(addr);
    if (!I2C_WaitAck()) { I2C_Stop(); return false; }
    for (uint8_t i = 0; i < len; i++) {
        I2C_SendByte(data[i]);
        if (!I2C_WaitAck()) { I2C_Stop(); return false; }
    }
    I2C_Stop();
    return true;
}

/**
 * @brief 软件 I2C 读函数（先写入寄存器地址，再连续读取）
 * @param addr I2C 设备地址
 * @param reg  起始寄存器地址
 * @param data 读取数据缓冲区
 * @param len  读取数据长度
 * @return true 表示读取成功
 */
bool Soft_I2C_Read(uint8_t addr, uint8_t reg, uint8_t *data, uint8_t len)
{
    I2C_Start();
    I2C_SendByte(addr);
    if (!I2C_WaitAck()) { I2C_Stop(); return false; }
    I2C_SendByte(reg);
    if (!I2C_WaitAck()) { I2C_Stop(); return false; }

    I2C_Start();
    I2C_SendByte(addr | 0x01);
    if (!I2C_WaitAck()) { I2C_Stop(); return false; }
    for (uint8_t i = 0; i < len; i++) {
        data[i] = I2C_ReadByte(i != len - 1);
    }
    I2C_Stop();
    return true;
}

//------------------------ 通用工具函数 ------------------------

/**
 * @brief 判断某年份是否为闰年
 */
bool IsLeapYear(uint32_t year) {
    return ((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0);
}

/**
 * @brief BCD 转 HEX（适用于 RTC 读取）
 */
unsigned char BcdToHex(uint8_t bcd) {
    return ((bcd >> 4) * 10 + (bcd & 0x0F));
}

/**
 * @brief HEX 转 BCD（适用于 RTC 设置）
 */
unsigned char HexToBcd(uint8_t hex) {
    return ((hex / 10) << 4) + (hex % 10);
}

/**
 * @brief 屏蔽 BM8563 读出的时间寄存器中的无效控制位
 */
void datajust(void)
{
    trdata[0] &= 0x7f;  // 秒
    trdata[1] &= 0x7f;  // 分
    trdata[2] &= 0x3f;  // 时
    trdata[3] &= 0x3f;  // 日
    trdata[4] &= 0x07;  // 周
    trdata[5] &= 0x1f;  // 月
}

/**
 * @brief 时间戳转换为本地时间结构（自定义）
 */
void LocalTime(uint32_t time, Tm *tm)
{
    tm->tm_sec = time % 60;
    time /= 60;
    tm->tm_min = time % 60;
    time /= 60;
    tm->tm_hour = time % 24;
    time /= 24;

    // 计算从 1970 年开始到当前的年份
    tm->tm_year = 1970;
    while (1) {
        uint32_t days_in_year = IsLeapYear(tm->tm_year) ? 366 : 365;
        if (time < days_in_year) break;
        time -= days_in_year;
        tm->tm_year++;
    }

    // 计算月份
    const unsigned int *mdays = monDays[IsLeapYear(tm->tm_year)];
    tm->tm_mon = 0;
    while (time >= mdays[tm->tm_mon]) {
        time -= mdays[tm->tm_mon];
        tm->tm_mon++;
    }
	//月份从零开始，需要+1
	tm->tm_mon++;
    tm->tm_mday = time + 1;
}


/**
* @brief 本地时间结构转时间戳（秒） 该时间戳为东八区的时间戳
 */
unsigned long mktime(uint32_t year, uint32_t mon, uint32_t day,
                     uint32_t hour, uint32_t min, uint32_t sec)
{
    if ((mon -= 2) <= 0) { mon += 12; year--; }
    return ((((year/4 - year/100 + year/400 + 367*mon/12 + day) +
              year*365 - 719499) * 24 + hour) * 60 + min) * 60 + sec - 28800 + (8 * 3600);
}

//------------------------ BM8563 驱动函数 ------------------------

/**
 * @brief 初始化 BM8563 寄存器，关闭中断、时钟输出等功能
 */
bool BM8563Init(void)
{
    Soft_I2C_Init();
    uint8_t buf1[] = { BM8563_CTR_STATUS_ADDR_1, 0x00 };
    uint8_t buf2[] = { BM8563_CTR_STATUS_ADDR_2, 0x00 };
    uint8_t buf3[] = { BM8563_CLOCKOUT, 0x00 };
    Soft_I2C_Write(BM8563ADDR, buf1, 2);
    Soft_I2C_Write(BM8563ADDR, buf2, 2);
    Soft_I2C_Write(BM8563ADDR, buf3, 2);
	
//	uint32_t timestamp = mktime(YEAR, MON, DAY, HOUR, MINI, SEC);
//	if(!SetTimeFromStamp(timestamp, 4)){
//		NRF_LOG_INFO("SetTimeFromStamp ERROR");
//	}
	
	return true;
}

/**
 * @brief 设置 BM8563 的时间寄存器（写入时间）
 */
uint8_t SetBM8563(uint8_t suba, uint8_t *s, uint8_t no)
{
    twdata[0] = suba;
    for (int i = 0; i < no; i++)
        twdata[i + 1] = s[i];
    return Soft_I2C_Write(BM8563ADDR, twdata, no + 1);
}

/**
 * @brief 读取 BM8563 寄存器内容
 */
uint8_t GetBM8563(uint8_t suba, uint8_t *s, uint8_t no)
{
    if (Soft_I2C_Read(BM8563ADDR, suba, trdata, no)) {
        for (int i = 0; i < no; i++) s[i] = trdata[i];
        return 1;
    }
    return 0;
}

/**
 * @brief 获取当前 RTC 时间，并返回时间戳（秒）
 */
uint32_t GetTimeStamp(void)
{
    if (!Soft_I2C_Read(BM8563ADDR, BM8563_DATA_START_ADDR, trdata, 7))
        return 0xffffffff;
    datajust();
    Times t;
    t.Second = BcdToHex(trdata[0]);
    t.Min    = BcdToHex(trdata[1]);
    t.Hour   = BcdToHex(trdata[2]);
    t.Day    = BcdToHex(trdata[3]);
    t.Mon    = BcdToHex(trdata[5]);
    t.Year   = BcdToHex(trdata[6]) + 2000;
    return mktime(t.Year, t.Mon, t.Day, t.Hour, t.Min, t.Second);
}

/**
 * @brief 根据时间戳设置 BM8563 的时间
 * @param mstamp 时间戳（单位：秒）
 * @param mzone  时区偏移（+8 表示东八区）
 * @param wday   星期几（0~6）
 * @return true 表示设置成功
 */
bool SetTimeFromStamp(uint32_t mstamp, uint32_t wday)
{
    Tm t;
    LocalTime(mstamp, &t);
    uint8_t data[7];
    data[0] = HexToBcd(t.tm_sec);
    data[1] = HexToBcd(t.tm_min);
    data[2] = HexToBcd(t.tm_hour);
    data[3] = HexToBcd(t.tm_mday);
    data[4] = HexToBcd(wday);  // 或 t.tm_wday
    data[5] = HexToBcd(t.tm_mon);
    data[6] = HexToBcd(t.tm_year - 2000);
    return SetBM8563(0x02, data, 7);
}

//时间获取函数
Tm get_local_time(void){
	uint32_t ts = GetTimeStamp();
	LocalTime(ts, &T);
	return T;
}
