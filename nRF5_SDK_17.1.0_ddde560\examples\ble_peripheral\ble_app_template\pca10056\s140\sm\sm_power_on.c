#include "sm_power_on.h"
#include "nrf_drv_gpiote.h"
#include "nrf_pwr_mgmt.h"
#include "sm.h"
#include "gui.h"
#include "twiCW2015.h"
#include "storage.h"
#include "power.h"
#include "bm8563.h"

#define POWER_ON_TIME 3000		//开机动画时间
#define POWER_BARR		20			//电池电量过低提示
#define low_check 0

volatile static uint32_t sm_on_tick_ms;		//开机计时器
uint32_t is_lock;													//设备加锁状态
uint8_t soc;
static bool low_soc;

void sm_power_on_init(void)
{
    //开机动画
    switch_to_next_screen(UI_SCREEN_LOG);

#if low_check
    //检查电池电量
    cw2015_read_soc_percent();
    if(get_SOC() < POWER_BARR) {
        low_soc = true;
    }

    //检查设备加锁状态
//    storage_read_lock(&is_lock);
#endif
    lgs4056_read_and_update_state();
	// 检查是否因充电器插入而启动
    extern bool early_charge_detected;
    if (early_charge_detected) {
        NRF_LOG_INFO("System wakeup by charger insertion detected");
    }
	Tm t;
	t = get_local_time();
	NRF_LOG_INFO("Time: %04d-%02d-%02d %02d:%02d:%02d",
             t.tm_year, t.tm_mon, t.tm_mday,
             t.tm_hour, t.tm_min, t.tm_sec);
}


void sm_power_on_tick(void)
{
    sm_on_tick_ms++;
    if(sm_on_tick_ms >= POWER_ON_TIME) {
        sm_on_tick_ms = 0;
        //检查是否加锁或者低电量
        if(low_soc == true) {
            sm_jump(SM_LOWSOC,0);
        } else {
            if(is_lock == 1) {
                sm_jump(SM_LOCK,0);
            } else {
                if(get_charg_sta() == CHARGE_STATE_IDLE) {
                    sm_jump(SM_READY,0);
                } else sm_jump(SM_CHARG,0);
            }
        }

    }
}


void sm_power_on_event(ui_evt_t ui_evt)
{
}


