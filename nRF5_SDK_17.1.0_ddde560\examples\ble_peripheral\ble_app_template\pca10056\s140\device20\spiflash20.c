#include "main.h"
#include "spiflash20.h"
#include "nrf_nvic.h"

//指令表

#define W25X_WriteEnable		  	0x06
#define W25X_WriteDisable		  	0x04
#define W25X_ReadStatusReg		  0x05
#define W25X_WriteStatusReg		  0x01
#define W25X_ReadData			  		0x03
#define W25X_FastReadData		  	0x0B
#define W25X_FastReadDual		  	0x3B
#define W25X_PageProgram		  	0x02
#define W25X_BlockErase			  	0xD8
#define W25X_SectorErase		  	0x20
#define W25X_ChipErase			  	0xC7
#define W25X_PowerDown			  	0xB9
#define W25X_ReleasePowerDown	  0xAB
#define W25X_DeviceID			  		0xAB
#define W25X_ManufactDeviceID	  0x90
#define W25X_JedecDeviceID		  0x9F   //9F    
#define FLASH_ID 0XBA6012  //器件ID
#define Dummy_Byte 0XFF
#define WIP_Flag  0x01


//芯片信息
#define FLASHPAGESIZE (256)
#define FALSHSECTORSIZE (4096)
#define FLASHBLOCKSIZE  (64*1024)

#define USER_SPI_CONFIG_IRQ_PRIORITY_HIGH 3

#define USRE_NRF_DRV_SPI_FLSH_CONFIG                         \
{                                                            \
    .sck_pin      = SPI_SCK_PIN,                            \
    .mosi_pin     = SPI_MOSI_PIN,                           \
    .miso_pin     = SPI_MISO_PIN,                           \
    .ss_pin       = NRF_DRV_SPI_PIN_NOT_USED,                \
    .irq_priority = USER_SPI_CONFIG_IRQ_PRIORITY_HIGH,         \
    .orc          = 0xFF,                                    \
    .frequency    = NRF_DRV_SPI_FREQ_500K,                   \
    .mode         = NRF_DRV_SPI_MODE_0,                      \
    .bit_order    = NRF_DRV_SPI_BIT_ORDER_MSB_FIRST,         \
}
#define SPI_BUFSIZE 16 //SPI缓存的大小

meas_record_t MT;

static const nrf_drv_spi_t spi = NRF_DRV_SPI_INSTANCE(2);  /**< SPI instance. */
static volatile bool spi_xfer_done;  /**< Flag used to indicate that SPI instance completed the transfer. */
volatile  uint8_t   SPIReadLength, SPIWriteLength;
uint8_t   SPI_Tx_Buf[SPI_BUFSIZE];  //发送
uint8_t   SPI_Rx_Buf[SPI_BUFSIZE];  //接收

void spi_event_handler(nrf_drv_spi_evt_t const * p_event, void *p_context)
{
    spi_xfer_done = true;
}

void spi_master_init(void)
{
    nrf_drv_spi_config_t spi_config = USRE_NRF_DRV_SPI_FLSH_CONFIG;
    spi_config.ss_pin   = NRF_DRV_SPI_PIN_NOT_USED;
    APP_ERROR_CHECK(nrf_drv_spi_init(&spi, &spi_config, spi_event_handler, NULL));
	
	nrf_gpio_cfg_output(SPI_SS_PIN);
}




/*
向flash中写入数据
参数  data 要写入的数据
*/
void spi_flash_write_reg(uint8_t data)
{
    spi_xfer_done = false;
    SPIWriteLength = 1;
    SPIReadLength = 0;
    SPI_Tx_Buf[0] = data;
    APP_ERROR_CHECK(nrf_drv_spi_transfer(&spi, SPI_Tx_Buf, SPIWriteLength, NULL, SPIReadLength));
    while(spi_xfer_done == false);
}
/*
从flash中读取数据
参数： reg 寄存器地址
*/
uint8_t spi_flash_read_reg(uint8_t reg)
{
    spi_xfer_done = false;
    SPI_Tx_Buf[0] = reg;
    APP_ERROR_CHECK(nrf_drv_spi_transfer(&spi, SPI_Tx_Buf, 0, SPI_Rx_Buf,1));
    while(spi_xfer_done == false);
    return SPI_Rx_Buf[0];
}

/*
读取flash的器件ID
*/
uint32_t spi_flash_ReadID(void)
{
    uint32_t temp = 0,temp0 = 0,temp1 = 0,temp2 = 0;
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_JedecDeviceID);
    temp0 = spi_flash_read_reg(0XFF);
    temp1 = spi_flash_read_reg(0XFF);
    temp2 = spi_flash_read_reg(0XFF);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    temp = (temp0 << 16)| (temp1 << 8) | temp2;
    return temp;
}

/*
写使能命令
*/
void spi_flash_WriteEnable(void)
{
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_WriteEnable);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选有效
}

/*
通过读状态寄存器等待FLASH芯片空闲
*/
void spi_flash_WaitForWriteEnd(void)
{
    unsigned char FLASH_Status = 0;
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ReadStatusReg); //发送读状态寄存器
    do {
        FLASH_Status = spi_flash_read_reg(Dummy_Byte);
    } while((WIP_Flag & FLASH_Status) == 1);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效

}
/*
擦除FLASH的扇区
参数 SectorAddr 要擦除的扇区地址
*/
void spi_flash_FLASH_SectorErase(uint32_t SectorAddr)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    spi_flash_WaitForWriteEnd();  //等待写完成
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_SectorErase);  //发送扇区擦除指令
    spi_flash_write_reg((SectorAddr & 0XFF0000) >> 16); //发送扇区擦除地址的高位
    spi_flash_write_reg((SectorAddr & 0XFF00) >> 8);
    spi_flash_write_reg(SectorAddr & 0XFF);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待擦除完成

    uint8_t check_buf[4];
    spi_flash_Flash_BufferRead(check_buf, SectorAddr, 4);
    if((check_buf[0] != 0xFF) || (check_buf[1] != 0xFF) ||
       (check_buf[2] != 0xFF) || (check_buf[3] != 0xFF)) {
        // NRF_LOG_ERROR("Sector erase failed!");
        NRF_LOG_INFO("Reset");
        nrf_delay_ms(100);
        NVIC_SystemReset();
    }
}

/*
FLASH页写入指令
参数：
备注：使用页写入指令最多可以一次向FLASH传输256个字节的数据
*/
void spi_flash_FLASH_PageWrite(unsigned char* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_PageProgram);  //发送写指令
    spi_flash_write_reg((WriteAddr & 0XFF0000) >> 16); //发送写地址的高位
    spi_flash_write_reg((WriteAddr & 0XFF00) >> 8);
    spi_flash_write_reg(WriteAddr & 0XFF);
    if(NumByteToWrite > 256) {
        NRF_LOG_INFO("write too large!\r\n");
        NRF_LOG_PROCESS();
        return ;
    }
    while(NumByteToWrite--) {
        spi_flash_write_reg(*pBuffer);
        pBuffer++;
    }
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待写完成
}
/*
从FLASH中读取数据
*/
void spi_flash_Flash_BufferRead(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead)
{
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ReadData);  //发送写指令
    spi_flash_write_reg((ReadAddr & 0XFF0000) >> 16); //发送写地址的高位
    spi_flash_write_reg((ReadAddr & 0XFF00) >> 8);
    spi_flash_write_reg(ReadAddr & 0XFF);
    while(NumByteToRead--) {
        *pBuffer = spi_flash_read_reg(Dummy_Byte);
        pBuffer++;
    }
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
}
/*
全片擦除
*/
void spi_flash_Chip_Erase(void)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ChipErase);  //全片擦除
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待写完成
}


void spi_flash_init(void)
{
    uint32_t ID = 0;
	spi_master_init();
    ID = spi_flash_ReadID();
    NRF_LOG_INFO("flash ID %d\r\n",ID);
    if(ID != FLASH_ID)
    {
        NRF_LOG_INFO("init w25q80 error\r\n");

    }
    else
    {
        NRF_LOG_INFO("init w25q80 ok!\r\n");
        NRF_LOG_INFO("FLASH ID is %X",ID);
    }
}

// ==================== 测量数据存储API实现 ====================

/*
计算指定索引对应的Flash地址
*/
static uint32_t meas_data_get_address(uint16_t index)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        return 0;  // 无效索引
    }
    return MEAS_DATA_START_ADDR + (index - 1) * MEAS_RECORD_SIZE;
}

/*
检查数据是否有效
*/
static bool meas_data_is_valid(const meas_record_t* data)
{
    return (data->is_valid == 0x12345678);  // 使用魔数标识有效数据
}

/*
写入测量数据到指定索引位置
*/
int meas_data_write(uint16_t index, const meas_record_t* data)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        NRF_LOG_ERROR("meas_data_write: Invalid index %d", index);
        return -1;  // 索引超出范围
    }

    if (data == NULL) {
        NRF_LOG_ERROR("meas_data_write: NULL data pointer");
        return -2;  // 无效参数
    }

    uint32_t flash_addr = meas_data_get_address(index);
    uint32_t sector_addr = (flash_addr / 4096) * 4096;  // 计算扇区地址

    // 读取整个扇区的数据
    uint8_t sector_buffer[4096];
    spi_flash_Flash_BufferRead(sector_buffer, sector_addr, 4096);

    // 修改对应位置的数据
    uint32_t offset_in_sector = flash_addr - sector_addr;
    memcpy(&sector_buffer[offset_in_sector], data, MEAS_RECORD_SIZE);

    // 擦除扇区
    spi_flash_FLASH_SectorErase(sector_addr);

    // 写回整个扇区
    for (uint32_t i = 0; i < 4096; i += 256) {
        uint16_t write_size = (4096 - i) > 256 ? 256 : (4096 - i);
        spi_flash_FLASH_PageWrite(&sector_buffer[i], sector_addr + i, write_size);
    }

    NRF_LOG_INFO("meas_data_write: Written data to index %d", index);
    return 0;  // 成功
}

/*
从指定索引位置读取测量数据
*/
int meas_data_read(uint16_t index, meas_record_t* data)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        NRF_LOG_ERROR("meas_data_read: Invalid index %d", index);
        return -1;  // 索引超出范围
    }

    if (data == NULL) {
        NRF_LOG_ERROR("meas_data_read: NULL data pointer");
        return -2;  // 无效参数
    }

    uint32_t flash_addr = meas_data_get_address(index);

    // 从Flash读取数据
    spi_flash_Flash_BufferRead((uint8_t*)data, flash_addr, MEAS_RECORD_SIZE);

    // 检查数据有效性
    if (!meas_data_is_valid(data)) {
        NRF_LOG_WARNING("meas_data_read: Invalid data at index %d", index);
        memset(data, 0, MEAS_RECORD_SIZE);  // 清空无效数据
        return -3;  // 数据无效
    }

    NRF_LOG_INFO("meas_data_read: Read data from index %d", index);
    return 0;  // 成功
}

/*
擦除指定索引位置的测量数据
*/
int meas_data_erase(uint16_t index)
{
    if (index < 1 || index > MAX_MEAS_RECORDS) {
        NRF_LOG_ERROR("meas_data_erase: Invalid index %d", index);
        return -1;  // 索引超出范围
    }

    uint32_t flash_addr = meas_data_get_address(index);
    uint32_t sector_addr = (flash_addr / 4096) * 4096;  // 计算扇区地址

    // 读取整个扇区的数据
    uint8_t sector_buffer[4096];
    spi_flash_Flash_BufferRead(sector_buffer, sector_addr, 4096);

    // 清空对应位置的数据
    uint32_t offset_in_sector = flash_addr - sector_addr;
    memset(&sector_buffer[offset_in_sector], 0xFF, MEAS_RECORD_SIZE);

    // 擦除扇区
    spi_flash_FLASH_SectorErase(sector_addr);

    // 写回整个扇区
    for (uint32_t i = 0; i < 4096; i += 256) {
        uint16_t write_size = (4096 - i) > 256 ? 256 : (4096 - i);
        spi_flash_FLASH_PageWrite(&sector_buffer[i], sector_addr + i, write_size);
    }

    NRF_LOG_INFO("meas_data_erase: Erased data at index %d", index);
    return 0;  // 成功
}

/*
擦除所有测量数据
*/
int meas_data_erase_all(void)
{
    // 计算需要擦除的扇区数量
    uint32_t total_size = MAX_MEAS_RECORDS * MEAS_RECORD_SIZE;
    uint32_t sectors_needed = (total_size + 4095) / 4096;  // 向上取整

    NRF_LOG_INFO("meas_data_erase_all: Erasing %d sectors", sectors_needed);

    // 擦除所有相关扇区
    for (uint32_t i = 0; i < sectors_needed; i++) {
        uint32_t sector_addr = MEAS_DATA_START_ADDR + (i * 4096);
        spi_flash_FLASH_SectorErase(sector_addr);
        NRF_LOG_INFO("Erased sector at address 0x%08X", sector_addr);
    }

    NRF_LOG_INFO("meas_data_erase_all: All measurement data erased");
    return 0;  // 成功
}

/*
获取有效数据的数量
*/
uint16_t meas_data_get_count(void)
{
    uint16_t count = 0;
    meas_record_t temp_data;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (meas_data_is_valid(&temp_data)) {
            count++;
        }
    }

    NRF_LOG_INFO("meas_data_get_count: Found %d valid records", count);
    return count;
}

/*
查找下一个空闲的存储位置
*/
uint16_t meas_data_find_next_free(void)
{
    meas_record_t temp_data;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (!meas_data_is_valid(&temp_data)) {
            NRF_LOG_INFO("meas_data_find_next_free: Found free slot at index %d", i);
            return i;
        }
    }

    NRF_LOG_WARNING("meas_data_find_next_free: No free slots available");
    return 0;  // 没有空闲位置
}

// ==================== 循环覆盖功能实现 ====================

/*
获取最旧数据的索引
*/
uint16_t meas_data_get_oldest_index(void)
{
    meas_record_t temp_data;
    uint16_t oldest_index = 0;
    uint32_t oldest_stamp = 0xFFFFFFFF;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (meas_data_is_valid(&temp_data) && temp_data.stamp < oldest_stamp) {
            oldest_stamp = temp_data.stamp;
            oldest_index = i;
        }
    }

    NRF_LOG_INFO("meas_data_get_oldest_index: Oldest record at index %d, stamp %lu", oldest_index, oldest_stamp);
    return oldest_index;
}

/*
获取最新数据的索引
*/
uint16_t meas_data_get_newest_index(void)
{
    meas_record_t temp_data;
    uint16_t newest_index = 0;
    uint32_t newest_stamp = 0;

    for (uint16_t i = 1; i <= MAX_MEAS_RECORDS; i++) {
        uint32_t flash_addr = meas_data_get_address(i);
        spi_flash_Flash_BufferRead((uint8_t*)&temp_data, flash_addr, MEAS_RECORD_SIZE);

        if (meas_data_is_valid(&temp_data) && temp_data.stamp > newest_stamp) {
            newest_stamp = temp_data.stamp;
            newest_index = i;
        }
    }

    NRF_LOG_INFO("meas_data_get_newest_index: Newest record at index %d, stamp %lu", newest_index, newest_stamp);
    return newest_index;
}

/*
循环写入测量数据（自动覆盖最旧数据）
*/
int meas_data_write_cyclic(const meas_record_t* data)
{
    if (data == NULL) {
        NRF_LOG_ERROR("meas_data_write_cyclic: NULL data pointer");
        return -1;
    }

    // 查找下一个空闲位置
    uint16_t next_free = meas_data_find_next_free();
    
    if (next_free > 0) {
        // 有空闲位置，直接写入
        NRF_LOG_INFO("meas_data_write_cyclic: Writing to free slot %d", next_free);
        return meas_data_write(next_free, data);
    } else {
        // 没有空闲位置，覆盖最旧的数据
        uint16_t oldest_index = meas_data_get_oldest_index();
        
        if (oldest_index == 0) {
            NRF_LOG_ERROR("meas_data_write_cyclic: No valid records to overwrite");
            return -1;
        }

        NRF_LOG_INFO("meas_data_write_cyclic: Overwriting oldest record at index %d", oldest_index);
        
        // 覆盖最旧的数据
        int result = meas_data_write(oldest_index, data);
        if (result == 0) {
            NRF_LOG_INFO("meas_data_write_cyclic: Successfully overwrote record at index %d", oldest_index);
        }
        return result;
    }
}

/*
获取存储状态信息
*/
int meas_data_get_status(uint16_t* total_records, uint16_t* valid_records, 
                        uint16_t* oldest_index, uint16_t* newest_index, bool* is_full)
{
    if (total_records == NULL || valid_records == NULL || 
        oldest_index == NULL || newest_index == NULL || is_full == NULL) {
        NRF_LOG_ERROR("meas_data_get_status: NULL pointer parameter");
        return -1;
    }

    *total_records = MAX_MEAS_RECORDS;
    *valid_records = meas_data_get_count();
    *oldest_index = meas_data_get_oldest_index();
    *newest_index = meas_data_get_newest_index();
    *is_full = (*valid_records >= MAX_MEAS_RECORDS);

    NRF_LOG_INFO("meas_data_get_status: Total=%d, Valid=%d, Oldest=%d, Newest=%d, Full=%s", 
                 *total_records, *valid_records, *oldest_index, *newest_index, 
                 *is_full ? "Yes" : "No");

    return 0;
}

//存储数据到flash
void save_data(uint32_t valid, uint32_t batt, uint32_t dark, uint32_t b, uint32_t g, uint32_t temp, uint32_t r, uint32_t stamp){
	MT.is_valid = valid;
	MT.raw_data_batt = batt;
	MT.raw_data_dark = dark;
	MT.raw_data_b10 = b;
	MT.raw_data_g100 = g;
	MT.temperature = temp;
	MT.result = r;
	MT.stamp = stamp;
}

//获取测量数据
meas_record_t get_measure_data(void){
	return MT;
}