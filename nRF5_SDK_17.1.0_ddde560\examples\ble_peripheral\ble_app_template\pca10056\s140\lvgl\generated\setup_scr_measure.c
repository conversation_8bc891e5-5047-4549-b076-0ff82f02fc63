/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "sm.h"

volatile bool stop_flag;
volatile bool is_save;
static lv_anim_t *current_anim = NULL;      // 保存当前动画引用

// 优化：使用自定义的slider更新回调函数
static void slider_value_update(void * var, int32_t v)
{
	if(stop_flag){
		if (current_anim) {
            lv_anim_del(var, NULL); // 删除动画
            current_anim = NULL;
        }
		stop_flag = 0;
		is_save = 0;
		sm_jump(SM_BABY,0);
		return;
	}
    // 批量更新，减少重绘次数
    lv_slider_set_value((lv_obj_t *)var, v, LV_ANIM_OFF);
    lv_obj_invalidate((lv_obj_t *)var); // 手动触发重绘
}

void slider_anim_ready_cb(lv_anim_t *a)
{
	current_anim = NULL; // 清空动画引用
	is_save = 1;
    sm_jump(SM_READY,0);
}

void setup_scr_measure(lv_ui *ui)
{
    //Write codes measure
    ui->measure = lv_obj_create(NULL);
    lv_obj_set_size(ui->measure, 320, 170);
    lv_obj_set_scrollbar_mode(ui->measure, LV_SCROLLBAR_MODE_OFF);

    //Write style for measure, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->measure, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->measure, lv_color_hex(0x030303), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->measure, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes measure_slider_1
    ui->measure_slider_1 = lv_slider_create(ui->measure);
    lv_slider_set_range(ui->measure_slider_1, 0, 100);
    lv_slider_set_mode(ui->measure_slider_1, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->measure_slider_1, 0, LV_ANIM_OFF);
    lv_obj_set_pos(ui->measure_slider_1, 80, 67);
    lv_obj_set_size(ui->measure_slider_1, 160, 16);

    //Write style for measure_slider_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->measure_slider_1, 102, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->measure_slider_1, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->measure_slider_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->measure_slider_1, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->measure_slider_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->measure_slider_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for measure_slider_1, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->measure_slider_1, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->measure_slider_1, lv_color_hex(0x47BEF8), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->measure_slider_1, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->measure_slider_1, 8, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for measure_slider_1, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->measure_slider_1, 0, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui->measure_slider_1, &_Frame_26x26, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_opa(ui->measure_slider_1, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_recolor_opa(ui->measure_slider_1, 0, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->measure_slider_1, 8, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes measure_label_1
    ui->measure_label_1 = lv_label_create(ui->measure);
    lv_label_set_text(ui->measure_label_1, "接触宝宝皮肤2秒");
    lv_label_set_long_mode(ui->measure_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->measure_label_1, 81, 110);
    lv_obj_set_size(ui->measure_label_1, 157, 28);

    //Write style for measure_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->measure_label_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->measure_label_1, &lv_font_Chinese_bold, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->measure_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->measure_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->measure_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->measure_label_1, lv_color_hex(0x030303), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->measure_label_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->measure_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of measure.

    static lv_anim_t a; // 改为static以便保存引用
    lv_anim_init(&a);
    lv_anim_set_var(&a, ui->measure_slider_1);
    lv_anim_set_exec_cb(&a, slider_value_update); // 使用优化的回调函数
    lv_anim_set_values(&a, 0, 100);
    lv_anim_set_time(&a, 800); 
    lv_anim_set_path_cb(&a, lv_anim_path_ease_in_out); // 使用缓动效果替代线性
    lv_anim_set_ready_cb(&a, slider_anim_ready_cb);
    
    // 优化：设置更高的动画刷新率
    lv_anim_set_repeat_count(&a, 0); // 不重复
    lv_anim_set_playback_time(&a, 0); // 不回放
    
	current_anim = &a; // 保存动画引用
    lv_anim_start(&a);
    
    //Update current screen layout.
    lv_obj_update_layout(ui->measure);
}