#ifndef __SM_H__
#define __SM_H__


#include "main.h"

//全局测量模式
typedef enum {
    SINGLE_MODE = 0,
    AVERAGE_MODE,
} sm_measure_mode_t;

//全局主界面模式
typedef enum {
    RESULT1_MODE = 0,
    RESULT1M_MODE,
} sm_measure_unit_t;

//全局状态机
typedef enum {
    SM_POWER_ON = 0,
    SM_READY,
    SM_MEASURE,
    SM_MEASURE_MODE,
    SM_MEASURE_UNIT,
    SM_RESULT,
    SM_BABY,
    SM_HISTORY,
    SM_CALIBRATE,
    SM_POWER_OFF,
    SM_LOCK,
    SM_ERROR,
    SM_LOWSOC,
    SM_BLE,
    SM_CHARG,
} sm_t;

//交互事件
typedef enum { //UI事件类型
    UI_EVT_TYPE_KEY = 0,        //按键
    UI_EVT_TYPE_MEASURE_DONE,   //测量完成
} ui_evt_type_t;

//按键事件类型
typedef enum {
    KEY_EVENT_TYPE_PRESSED = 0,
    KEY_EVENT_TYPE_RELEASED,
    KEY_EVENT_TYPE_LONG_PRESS,
    KEY_EVENT_TYPE_DOWN,
    KEY_EVENT_TYPE_DOUBLE_CLICK,
} key_evt_type_t;

//按键编号
typedef enum {
    KEY_INSTANCE_KEY_USER = 0,
    KEY_INSTANCE_KEY_PRESS,
    KEY_INSTANCE_KEY_UP,
    KEY_INSTANCE_KEY_DOWN
} key_instance_t;

//按键事件
typedef struct {
    key_instance_t key_instance;    //按键编号
    key_evt_type_t key_evt_type;    //按键类型
    uint32_t key_pressed_ms;        //长按时间
} ui_evt_key_t;


//测量完成事件
typedef struct {
    uint32_t dark;
    uint32_t green;
    uint32_t blue;
    float tempture;
} ui_evt_measure_done_t;

//UI事件结构体
typedef struct {
    ui_evt_type_t ui_evt_type;//UI事件类型
    union {
        ui_evt_key_t ui_evt_key;
        ui_evt_measure_done_t ui_evt_measure_done;
    } evt; //各类事件内容
} ui_evt_t;

//界面类型
typedef enum {
    UI_SCREEN_LOG = 0,             // 登录界面
    UI_SCREEN_BABY2S,              // baby2s界面
    UI_SCREEN_BLUEDIS,             // 蓝牙断开界面
    UI_SCREEN_BLUETOOTHCONNECT,    // 蓝牙连接界面s
    UI_SCREEN_CHARGED,             // 充电界面
    UI_SCREEN_LOCK,                // 锁定界面
    UI_SCREEN_LOWBATTERY,          // 低电量界面
    UI_SCREEN_RESULT1,             // 结果1界面
    UI_SCREEN_SINGLE,              // 单次界面
    UI_SCREEN_AVERAGE,             // 平均值界面
    UI_SCREEN_MEASURE,				//测量界面
	UI_SCREEN_HISTORY,				//历史界面
} ui_screen_t;

void system_auto_power_off_timer_reload(void);
sm_t sm_get(void);
void sm_jump(sm_t new_sm, uint32_t para);
void sm_event(ui_evt_t ui_evt);
void sm1_init(void);
void sm_set_auto_power_off_time(uint32_t second);
uint32_t sm_get_power_on_time(void);
void sm_open_timer(void);
void sm_close_timer(void);
bool get_measure_unit(void);
void set_measure_unit(sm_measure_unit_t unit);
#endif
