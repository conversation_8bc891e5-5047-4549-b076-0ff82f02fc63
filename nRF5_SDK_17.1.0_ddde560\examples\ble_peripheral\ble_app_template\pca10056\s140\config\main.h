#ifndef __MIAN_H__
#define __MIAN_H__

#include <stdint.h>
#include <string.h>
#include <stdio.h>

#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

#include "nordic_common.h"
#include "nrf.h"
#include "ble.h"
#include "nrf_gpio.h"
#include "nrf_delay.h"
#include "app_error.h"
#include "app_timer.h"
#include "app_fifo.h"
#include "app_util_platform.h"

//DEBUG
#define Log(...)				NRF_LOG_INFO(__VA_ARGS__)

//引脚映射计算
#define NRF_GPIO_PIN_MAP(port, pin) (((port) << 5) | ((pin) & 0x1F))

//升压芯片EN脚
#define PWM_LED_PIN     (17)//17

//SPI_FLASH引脚
#define SPI_SCK_PIN		(20)
#define	SPI_MOSI_PIN	(22)
#define	SPI_MISO_PIN	(13)
#define	SPI_SS_PIN		(15)

//TWI驱动CW2015电量计
#define CW2015_SCL	NRF_GPIO_PIN_MAP(1,4)
#define CW2015_SDA	NRF_GPIO_PIN_MAP(1,2)

//TWI驱动PCB2温度传感器
#define TEMP_SCL 		NRF_GPIO_PIN_MAP(0,19)
#define TEMP_SDA 		NRF_GPIO_PIN_MAP(0,16)

#define LGS4056_CHRG_PIN     NRF_GPIO_PIN_MAP(1,10)  // CHRG 引脚
#define LGS4056_DONE_PIN     NRF_GPIO_PIN_MAP(1,11)  // DONE 引脚

//ADC
#define ADC_AMP1_PIN           	NRF_SAADC_INPUT_AIN2
#define ADC_AMP2_PIN            NRF_SAADC_INPUT_AIN3
#define ADC_AMP1_CHANNEL       	2
#define ADC_AMP2_CHANNEL        3


//LCD屏幕参数	清风开发板
//#define GPIO_LCD_BK					(NRF_GPIO_PIN_MAP(1,10))//0 27
//#define GPIO_LCD_RES				(NRF_GPIO_PIN_MAP(1,11))//0 26
//#define GPIO_LCD_DC					(NRF_GPIO_PIN_MAP(1,14))//0 29
//#define GPIO_LCD_MOSI				(NRF_GPIO_PIN_MAP(1,15))//0 31
//#define GPIO_LCD_SCK				(NRF_GPIO_PIN_MAP(1,13))//1 15
//#define GPIO_LCD_CS					(NRF_GPIO_PIN_MAP(1,12))//1 13

//#define GPIO_KEY_DOWN				(NRF_GPIO_PIN_MAP(0,11))//sw1
//#define GPIO_KEY_UP					(NRF_GPIO_PIN_MAP(0,12))//sw2
//#define GPIO_KEY_USER				(NRF_GPIO_PIN_MAP(0,24))//sw3
//#define GPIO_KEY_PRESS			(NRF_GPIO_PIN_MAP(0,25))//sw4


#define GPIO_LCD_BK					(NRF_GPIO_PIN_MAP(0,27))
#define GPIO_LCD_RES				(NRF_GPIO_PIN_MAP(0,2))
#define GPIO_LCD_DC					(NRF_GPIO_PIN_MAP(0,29))
#define GPIO_LCD_MOSI				(NRF_GPIO_PIN_MAP(0,31))
#define GPIO_LCD_SCK				(NRF_GPIO_PIN_MAP(1,15))
#define GPIO_LCD_CS					(NRF_GPIO_PIN_MAP(1,12))

//KEY参数
#define GPIO_KEY_DOWN				(NRF_GPIO_PIN_MAP(1,9))//key_down sw3
#define GPIO_KEY_UP					(NRF_GPIO_PIN_MAP(1,8))//key_up sw2
#define GPIO_KEY_USER				(NRF_GPIO_PIN_MAP(0,6))//key_user	sw1
#define GPIO_KEY_PRESS			(NRF_GPIO_PIN_MAP(0,14))//key_press k1


//LED开关芯片
#define LED_G_CUT_PIN   (12)
#define LED_B_CUT_PIN   (11)


//#define TIME_STAMP (1709107553)
#define MEAS_RECORD_NUM		        (20)	//测量记录的数量
#define AUTO_POWER_OFF_SECOND       (180)    //自动关机时间，单位秒

//总胆红素摩尔质量
#define TBIL_MOLAR_MASS 584.68f

//FLASH
//UICR与Flash的不同的是只有通过全片擦除后才能重新写入，不能单独擦除。
//MAC地址：芯片出厂就有，固定位于FICR中的0x100000A4~0x100000A8中
//产测标志位：生产时烧录，配置为UICR的0x100000A4中
//序列号：生产时烧录，配置为UICR的0x100000A8中
#define UICR_TEST_FLAG          	(0x10001080)
#define UICR_SERIAL_NUMBER      	(0x10001084)
//校准参数、用户记录、产测结果、加锁、序列号的保存位置
#define CALI_USER_FILE_ID     		(0x0001)
#define CALI_USER_KEY         		(0x0001)
//医院采集数据记录的保存位置
#define RECORD_FILE_ID     			(0x0001)
#define RECORD_KEY         			(0x0002)

#define CALI_AVERAGE_TIMES   		(5)		//校准时取几次数据的均值
#define MEASURE_AVERAGE_TIMES   	(3)		//使用时取几次数据的均值
#define BLE_DEBUG					(1)		//通过BLE打印调试数据
#define BLE_COMM					(1)		//通过BLE传输数据给APP
#define DISPLAY_POINT_NUM			(1)		//数据显示到小数点后几位        //divid     2修正为1，显示小数点后1位
#define FIXEDCALDATA
#define USERTCDEADLINE
#define DEBUG_DATA_MODE (0)    //内部采集数据的时候为1，量产为0   
#define RESULT_UNIT (1) //1为普通显示，2为另外的单位

//锂电开关引脚
#define POWER_EN_PIN    (NRF_GPIO_PIN_MAP(1,11))

uint32_t nus_send_byte(uint8_t* data, uint16_t len);
extern char ble_name_buf[20];

#endif
