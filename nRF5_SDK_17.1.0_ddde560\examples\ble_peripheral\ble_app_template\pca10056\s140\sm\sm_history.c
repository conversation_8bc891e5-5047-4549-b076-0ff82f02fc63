#include "sm_history.h"
#include "sm_ready.h"
#include "spiflash20.h"
#include "gui.h"
#include "sm_ble.h"
#include "twiCW2015.h"
#include "bm8563.h"
#include "sm_measure.h"  // �������ͷ�ļ���ʹ��result_show����
#include <stdio.h>

volatile static uint32_t sm5_tick_ms;

// ��ʷ����״̬����
static uint16_t current_history_index = 1;  // ��ǰ��ʾ����ʷ��¼����
static uint16_t total_history_count = 0;    // �ܵ���ʷ��¼����

// ��ʷ����ר�ú�������
static void load_history_record(uint16_t index, lv_ui *ui);
static void update_history_display(lv_ui *ui);
static void format_timestamp_display(uint32_t timestamp, char *date_str, size_t max_len);
static void show_no_data_display(lv_ui *ui);

// ȫ��UI�����ڳ�ʼ��ʱ����
static lv_ui g_history_ui;

void sm_history_init(void)
{
    sm5_tick_ms = 0;

    // ��ת����ʷ����
    switch_to_next_screen(UI_SCREEN_HISTORY);
	
	cw2015_read_soc_percent();
	
	// ��ȡUI����
    g_history_ui = get_ui();

    // ��ʾ�������ӱ�ʶ
    if(get_ble_con() == 1){
        bluetooth_image_set_visible(true);
    } else {
        bluetooth_image_set_visible(false);
    }

    // ��ȡ��ʷ��¼����
    total_history_count = meas_data_get_count();

    // ��ʼ����ʾ
    if (total_history_count > 0) {
        // �����ݣ���ʾ��һ����¼
        current_history_index = 1;
        load_history_record(current_history_index, &g_history_ui);
    } else {
        // û�����ݣ���ʾ������״̬
        current_history_index = 1;
        show_no_data_display(&g_history_ui);
        update_history_display(&g_history_ui);
    }
}


void sm_history_tick(void)
{
    sm5_tick_ms++;
	if(sm5_tick_ms >= 5000) {
        sm5_tick_ms = 0;
        battery_set_level(get_SOC());
    }
}


void sm_history_event(ui_evt_t ui_evt)
{
    if (ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) {
        if (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED) {
            switch (ui_evt.evt.ui_evt_key.key_instance) {
                case KEY_INSTANCE_KEY_USER:
                    // user�����̰��˳���ʷ����
                    sm_jump(SM_READY, 0);
                    break;

                case KEY_INSTANCE_KEY_UP:
                    // �ϼ���index++����ʾ��һ����¼
                    if (total_history_count > 0 && current_history_index < total_history_count) {
                        current_history_index++;
                        load_history_record(current_history_index, &g_history_ui);
                    }
                    break;

                case KEY_INSTANCE_KEY_DOWN:
                    // �¼���index--����ʾ��һ����¼
                    if (total_history_count > 0 && current_history_index > 1) {
                        current_history_index--;
                        load_history_record(current_history_index, &g_history_ui);
                    }
                    break;

                default:
                    // ��������������
                    break;
            }
        }
        else if (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_DOUBLE_CLICK) {
            // Double click event handling
            if (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER) {
                // User key double click: toggle unit
                sm_history_toggle_unit();
            }
        }
    }
}

// ==================== ��ʷ����ר�ú���ʵ�� ====================

/*
����ָ����������ʷ��¼����ʾ
*/
static void load_history_record(uint16_t index, lv_ui *ui)
{
    if (ui == NULL) return;

    meas_record_t data;
    int result = meas_data_read(index, &data);

    if (result == 0) {
        // �ɹ���ȡ���ݣ�������ʾ

        // 1. ����������ʾ - ʹ��get_local_timeת��ʱ���
        char date_str[32];
        format_timestamp_display(data.stamp, date_str, sizeof(date_str));

        // ����history_label_1��ʾ����
        lv_label_set_text(ui->history_label_1, date_str);

        // 2. ������ֵ��ʾ - Apply unit conversion
        char value_str[16];
        float display_value = (float)data.result / 1000.0f;

        // Convert value based on current unit setting
        if (get_measure_unit() == RESULT1M_MODE) {
            // Convert to mg/dL: μmol/L * 17.1 / 1000 = mg/dL
            display_value = display_value * 17.1f / 1000.0f;
            snprintf(value_str, sizeof(value_str), "%.1f", display_value);
        } else {
            // Display as μmol/L (original unit)
            snprintf(value_str, sizeof(value_str), "%.2f", display_value);
        }

        lv_label_set_text(ui->history_label_2, value_str);

        // ��ʾ��ֵ��ǩ
        lv_obj_clear_flag(ui->history_label_2, LV_OBJ_FLAG_HIDDEN);

    } else {
        // ��ȡʧ�ܻ�������Ч����ʾ������״̬
        show_no_data_display(ui);
    }

    // 3. ������ʷ��¼������ʾ (��ǰindex/����)
    update_history_display(ui);
}

/*
������ʷ��¼������ʾ
*/
static void update_history_display(lv_ui *ui)
{
    if (ui == NULL) return;

    char count_str[16];
	if(total_history_count == 0){
		snprintf(count_str, sizeof(count_str), "%d/%d", 0, 0);
	}else snprintf(count_str, sizeof(count_str), "%d/%d", current_history_index, total_history_count);
    lv_label_set_text(ui->history_label_4, count_str);
}

/*
��ʽ��ʱ���Ϊ��ʾ�ַ���
*/
static void format_timestamp_display(uint32_t timestamp, char *date_str, size_t max_len)
{
    if (timestamp == 0) {
        snprintf(date_str, max_len, "____ / __ / __  __ : __");
        return;
    }

    // ʹ��get_local_time����ת��ʱ���
    Tm local_time;
    LocalTime(timestamp, &local_time);

    // ��ʽ��Ϊ YYYY/MM/DD HH:MM
    snprintf(date_str, max_len, "%04d/%02d/%02d %02d:%02d",
             local_time.tm_year, local_time.tm_mon, local_time.tm_mday,
             local_time.tm_hour, local_time.tm_min);
}

/*
��ʾ������״̬
*/
static void show_no_data_display(lv_ui *ui)
{
    if (ui == NULL) return;

    // ������ʾϸ�»���
    lv_label_set_text(ui->history_label_1, "____ / __ / __  __ : __");

    // result��ʾ���»���
    lv_label_set_text(ui->history_label_2, "___ . __");

    // ��ʾ��ֵ��ǩ
    lv_obj_clear_flag(ui->history_label_2, LV_OBJ_FLAG_HIDDEN);
}

// ==================== ���ԺͲ��Ժ��� ====================

/*
��ȡ��ǰ��ʷ����״̬��Ϣ�����ڵ��ԣ�
*/
void sm_history_get_status(uint16_t *current_index, uint16_t *total_count)
{
    if (current_index) *current_index = current_history_index;
    if (total_count) *total_count = total_history_count;
}

/*
�ֶ�ˢ����ʷ������ʾ�������ⲿ���ã�
*/
void sm_history_refresh(void)
{
    // ���»�ȡ������������������д�룩
    total_history_count = meas_data_get_count();

    // ȷ����ǰ��������Ч��Χ��
    if (current_history_index > total_history_count && total_history_count > 0) {
        current_history_index = total_history_count;
    } else if (total_history_count == 0) {
        current_history_index = 1;
    }

    // ���¼��ص�ǰ��¼
    if (total_history_count > 0) {
        load_history_record(current_history_index, &g_history_ui);
    } else {
        show_no_data_display(&g_history_ui);
        update_history_display(&g_history_ui);
    }
}

/*
Toggle unit in history interface and refresh display
*/
void sm_history_toggle_unit(void)
{
    // Toggle the global unit setting
    if (get_measure_unit() == RESULT1_MODE) {
        set_measure_unit(RESULT1M_MODE);
    } else {
        set_measure_unit(RESULT1_MODE);
    }

    // Update unit label in history interface
    sm_history_update_unit_display(&g_history_ui);

    // Refresh current record display with new unit
    if (total_history_count > 0) {
        load_history_record(current_history_index, &g_history_ui);
    }
}

/*
Update unit display in history interface
*/
static void sm_history_update_unit_display(lv_ui *ui)
{
    if (ui == NULL) return;

    if (get_measure_unit() == RESULT1_MODE) {
        lv_label_set_text(ui->history_label_3, "mg/dL");
    } else {
        lv_label_set_text(ui->history_label_3, "μmol/L");
    }
}
