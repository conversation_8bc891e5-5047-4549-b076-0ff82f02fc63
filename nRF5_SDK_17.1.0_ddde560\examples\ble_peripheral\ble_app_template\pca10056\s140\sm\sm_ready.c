#include "sm_ready.h"
#include "frame.h"
#include "sm_history.h"
#include "hide_trig.h"
#include "power.h"
#include "storage.h"
#include "utils.h"
#include "algo.h"
#include "motor.h"
#include "boards.h"
#include "key.h"
#include "ST7789.h"
#include "gui.h"
#include "twiCW2015.h"
#include "sm_measure.h"
#include "sm_measure_mode.h"
#include "sm_ble.h"



volatile static uint32_t sm8_tick_ms;
volatile static uint32_t SOC_tick_ms;
//三次测量取均值
uint32_t average_times_total = 3;//表示通过几次测量计算均值
uint32_t average_times_now = 0;//目前已经得到几次数据
uint32_t bilirubin_average = 0;//用于求平均值

// 分离的测量结果存储
uint32_t single_measurement_result = 0;  // 单次测量结果
float average_measurement_results[3] = {0, 0, 0};  // 平均值模式下的三次测量结果
uint32_t average_final_result = 0;  // 平均值模式的最终结果
bool average_results_displayed[3] = {false, false, false};  // 跟踪哪些结果已经显示过

uint32_t measure_model_set = 0;//用于缓存APP设置的测量模式，因为正在测量中时不能直接改变
static uint32_t is_sn_showed = 0;//是否已经显示了序列号

//是否测量成功/是否保存
extern volatile bool is_save;

//测量数据
meas_record_t M;

//标记是从测量界面过来的
bool measure_flag;

void sm_ready_init(void)
{
    sm8_tick_ms = 0;

    //开启关机定时器
    sm_open_timer();
    //进入主界面
    switch_to_next_screen(UI_SCREEN_RESULT1);
	
	cw2015_read_soc_percent();
	
	//显示蓝牙连接标识
	if(get_ble_con() == 1){
        bluetooth_image_set_visible(true);
	}else  bluetooth_image_set_visible(false);
	
	if(measure_flag){
		average_times_now++;
	}
    // 显示测量结果
    if (get_measure_mode() == SINGLE_MODE) {
        // 单次测量模式：显示单次测量结果
        float result = get_result();
        if (result > 0.0f) {
            show_bilirubin(result);
			//保存数据
			if(is_save){
				M = get_measure_data();
				meas_data_write_cyclic(&M);
			}
        } else {
            show_bilirubin(0.0f);
        }
    } else {
        // 在平均值模式下，如果还没有开始测量或者已经完成所有测量，则重置显示标志
        // 平均值测量模式：检查是否有最终结果
        if (average_times_now == average_times_total && average_final_result > 0) {
            // 有最终平均值，显示在下方
            clear_average_bottom_lines();
            update_average_display();
			//保存数据
			if(is_save){
				M = get_measure_data();
				meas_data_write_cyclic(&M);
			}
        } else {
            // 没有最终结果，显示下划线
            show_bilirubin(0.0f);
        }
        // 显示已完成但未显示的测量结果
        for (int i = 0; i < average_times_now; i++) {
            if (!average_results_displayed[i]) {
                float display_value = average_measurement_results[i];
                if (display_value > 0) {  // 只显示有效的测量结果
                    if (get_measure_unit() == RESULT1M_MODE) {
                        display_value = display_value * 17.1f;  // mg/dL 转 μmol/L
                    }
                    show_average_measurement_result(i, display_value);
                }
                average_results_displayed[i] = true;
            }
        }
		
    }
	if(measure_flag){
		if (average_times_now == average_times_total) {
			// 重置显示标志
			for (int i = 0; i < 3; i++) {
				average_results_displayed[i] = false;
				average_measurement_results[i] = 0;
			}
			average_times_now = 0;
		}
		//显示之后
		bilirubin_average = 0;
		average_final_result = 0;
		 // 增加测量计数器
		measure_flag = 0;
	}
}


void sm_ready_tick(void)
{
    sm8_tick_ms++;
    SOC_tick_ms++;
    if(SOC_tick_ms >= 5000) {
        SOC_tick_ms = 0;
        battery_set_level(get_SOC());
    }
}

void sm_ready_event(ui_evt_t ui_evt)
{

    //探头按键
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_PRESS)) {
        if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_DOWN) {
            //按下进入测量模式
            sm_jump(SM_MEASURE,0);
        }
    }
    //UP按键
    else if((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_UP)) {
        if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED) {
            //UP键短按
			sm_jump(SM_HISTORY,0);
        }
    }
    //DOWN按键

    //用户按键
    else if((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER)) {
        if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_LONG_PRESS) {
            //用户键长按关机
            sm_jump(SM_POWER_OFF,0);
        } else if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED) {
            //用户键短按切换测量模式
            sm_jump(SM_MEASURE_MODE,0);
        } else if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_DOUBLE_CLICK) {
            //用户双击
            sm_jump(SM_MEASURE_UNIT,0);
        }
    }

}
