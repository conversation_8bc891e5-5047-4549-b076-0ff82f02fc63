#ifndef __SPIFLASH20_H__
#define __SPIFLASH20_H__

//ZD25WD20B  2M-bit

//Flexible Architecture for Code and Data Storage
//- Uniform 256-byte PageProgram
//- Uniform 256-byte Page Erase
//- Uniform 4K-byte Sector Erase
//- Uniform 32K/64K-byte Block Erase

#include "main.h"
#include "nrf_drv_spi.h"

// 测量数据结构定义
typedef struct {
    uint32_t is_valid;
    uint32_t raw_data_batt;
    uint32_t raw_data_dark;
    uint32_t raw_data_b10;
    uint32_t raw_data_g100;
    uint32_t temperature;     // 温度数据 (摄氏度*1000倍，例如25.5°C存储为25500)
    uint32_t result;          // mg/dL单位*1000倍
    uint32_t stamp;
} meas_record_t;

// 存储配置
#define MAX_MEAS_RECORDS        500
#define MEAS_RECORD_SIZE        sizeof(meas_record_t)  // 现在是32字节 (8个uint32_t字段)
#define MEAS_DATA_START_ADDR    0x1000  // 从4KB地址开始存储测量数据
#define MEAS_RECORDS_PER_SECTOR (4096 / MEAS_RECORD_SIZE)  // 每个扇区能存储的记录数 (约128条)

void spi_event_handler(nrf_drv_spi_evt_t const * p_event, void *p_context);

void spi_master_init(void);

/*
向flash中写入数据
参数  data 要写入的数据
*/
void spi_flash_write_reg(uint8_t data);

/*
从flash中读取数据
参数： reg 寄存器地址
*/
uint8_t spi_flash_read_reg(uint8_t reg);


/*
读取flash的器件ID
*/
uint32_t spi_flash_ReadID(void);


/*
写使能命令
*/
void spi_flash_WriteEnable(void);


/*
通过读状态寄存器等待FLASH芯片空闲
*/
void spi_flash_WaitForWriteEnd(void);

/*
擦除FLASH的扇区
参数 SectorAddr 要擦除的扇区地址
*/
void spi_flash_FLASH_SectorErase(uint32_t SectorAddr);


/*
FLASH页写入指令
参数：
备注：使用页写入指令最多可以一次向FLASH传输256个字节的数据
*/
void spi_flash_FLASH_PageWrite(unsigned char* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);

/*
从FLASH中读取数据
*/
void spi_flash_Flash_BufferRead(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead);

/*
全片擦除
*/
void spi_flash_Chip_Erase(void);



void spi_flash_init(void);

// 测量数据存储API
/*
写入测量数据到指定索引位置
参数：
  index: 数据索引 (1-500)
  data: 要写入的测量数据指针
返回值：
  0: 成功
  -1: 索引超出范围
  -2: Flash操作失败
*/
int meas_data_write(uint16_t index, const meas_record_t* data);

/*
从指定索引位置读取测量数据
参数：
  index: 数据索引 (1-500)
  data: 用于存储读取数据的缓冲区指针
返回值：
  0: 成功
  -1: 索引超出范围
  -2: Flash操作失败
  -3: 数据无效
*/
int meas_data_read(uint16_t index, meas_record_t* data);

/*
擦除指定索引位置的测量数据
参数：
  index: 数据索引 (1-500)
返回值：
  0: 成功
  -1: 索引超出范围
  -2: Flash操作失败
*/
int meas_data_erase(uint16_t index);

/*
擦除所有测量数据
返回值：
  0: 成功
  -1: Flash操作失败
*/
int meas_data_erase_all(void);

/*
获取有效数据的数量
返回值：
  有效数据的数量 (0-500)
*/
uint16_t meas_data_get_count(void);

/*
查找下一个空闲的存储位置
返回值：
  下一个空闲位置的索引 (1-500)
  0: 没有空闲位置
*/
uint16_t meas_data_find_next_free(void);

/*
循环写入测量数据（自动覆盖最旧数据）
参数：
  data: 要写入的测量数据指针
返回值：
  写入位置的索引 (1-500)
  -1: 写入失败
*/
int meas_data_write_cyclic(const meas_record_t* data);

/*
获取最旧数据的索引
返回值：
  最旧数据的索引 (1-500)
  0: 没有数据
*/
uint16_t meas_data_get_oldest_index(void);

/*
获取最新数据的索引
返回值：
  最新数据的索引 (1-500)
  0: 没有数据
*/
uint16_t meas_data_get_newest_index(void);

/*
获取存储状态信息
参数：
  total_records: 总记录数
  valid_records: 有效记录数
  oldest_index: 最旧记录索引
  newest_index: 最新记录索引
  is_full: 是否已满
返回值：
  0: 成功
  -1: 失败
*/
int meas_data_get_status(uint16_t* total_records, uint16_t* valid_records, 
                        uint16_t* oldest_index, uint16_t* newest_index, bool* is_full);


//循环存储
void save_data(uint32_t valid, uint32_t batt, uint32_t dark, uint32_t b, uint32_t g, uint32_t temp, uint32_t r, uint32_t stamp);
//获取测量数据
meas_record_t get_measure_data(void);
#endif
