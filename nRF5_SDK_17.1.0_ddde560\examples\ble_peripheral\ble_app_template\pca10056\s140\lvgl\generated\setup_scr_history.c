/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/
#include "lvgl.h"
#include "gui_guider.h"
#include "widgets_init.h"
#include "gui.h"

void setup_scr_history(lv_ui *ui)
{
    // 创建主屏幕容器
    ui->history = lv_obj_create(NULL);
    lv_obj_set_size(ui->history, 320, 170);
    lv_obj_set_scrollbar_mode(ui->history, LV_SCROLLBAR_MODE_OFF);

    // 设置背景样式
    lv_obj_set_style_bg_opa(ui->history, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->history, lv_color_hex(0x030303), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->history, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 创建日期标签 (history_label_1)
    ui->history_label_1 = lv_label_create(ui->history);
    lv_label_set_text(ui->history_label_1, "____ / __ / __  __ : __");
    lv_label_set_long_mode(ui->history_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->history_label_1, 24, 36);
    lv_obj_set_size(ui->history_label_1, 167, 18);
    lv_obj_set_style_border_width(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->history_label_1, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->history_label_1, &lv_font_montserratMedium_18, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->history_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->history_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->history_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 创建数值标签 (history_label_2)
    ui->history_label_2 = lv_label_create(ui->history);
    lv_label_set_text(ui->history_label_2, "___ . __");
    lv_label_set_long_mode(ui->history_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->history_label_2, 24, 66);
    lv_obj_set_size(ui->history_label_2, 199, 58);
    lv_obj_set_style_border_width(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->history_label_2, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->history_label_2, &lv_font_montserratMedium_58, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->history_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->history_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->history_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 创建单位标签 (history_label_3)
    ui->history_label_3 = lv_label_create(ui->history);
    lv_label_set_text(ui->history_label_3, "mg/dL");
    lv_label_set_long_mode(ui->history_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->history_label_3, 234, 102);
    lv_obj_set_size(ui->history_label_3, 62, 18);
    lv_obj_set_style_border_width(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->history_label_3, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->history_label_3, &lv_font_montserratMedium_17, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->history_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->history_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->history_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 创建历史记录计数标签 (history_label_4)
    ui->history_label_4 = lv_label_create(ui->history);
    lv_label_set_text(ui->history_label_4, "1/500");
    lv_label_set_long_mode(ui->history_label_4, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->history_label_4, 245, 36);
    lv_obj_set_size(ui->history_label_4, 54, 18);
    lv_obj_set_style_border_width(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->history_label_4, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->history_label_4, &lv_font_montserratMedium_18, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->history_label_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->history_label_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->history_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 创建蓝色上升图片 (history_img_3)
    ui->history_img_3 = lv_img_create(ui->history);
    lv_obj_add_flag(ui->history_img_3, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->history_img_3, &_drop_alpha_32x15);
    lv_img_set_pivot(ui->history_img_3, 50,50);
    lv_img_set_angle(ui->history_img_3, 0);
    lv_obj_set_pos(ui->history_img_3, 234, 72);
    lv_obj_set_size(ui->history_img_3, 32, 15);
    lv_obj_set_style_img_recolor_opa(ui->history_img_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->history_img_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->history_img_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->history_img_3, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 创建自定义组件
    battery_create(ui->history);
    number_square_create(ui->history, 1);
    bluetooth_image_create(ui->history);
    draw_gradient_line_circle_anim(ui->history, 0);
}
