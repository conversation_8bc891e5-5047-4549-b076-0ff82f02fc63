#include "sm_measure.h"
#include "sm_ready.h"
#include "analog.h"
#include "motor.h"
#include "algo.h"
#include "frame.h"
#include "power.h"
#include "storage.h"
#include "gui.h"
#include "nrf_drv_saadc.h"
#include "saadc.h"
#include "nrf_nvic.h"
#include "nrf_delay.h"
#include "gui_guider.h"
#include "sm_measure_mode.h"
#include "twiTMP.h"
#include "twiCW2015.h"
#include "bm8563.h"

volatile static uint32_t sm7_tick_ms;
static uint32_t press_key_resealed = 0;//是否松开了按压按键
static uint32_t colorChannal = 0x21;//通道数   20代表不是校准模式的测量
static float R;
// 外部变量声明（在sm_ready.c中定义）
extern uint32_t average_times_total;
extern uint32_t average_times_now;
extern uint32_t bilirubin_average;
extern uint32_t measure_model_set;
extern uint32_t single_measurement_result;  // 单次测量结果
extern float average_measurement_results[3];  // 平均值模式下的三次测量结果
extern uint32_t average_final_result;  // 平均值模式的最终结果
extern bool measure_flag;

void sm_measure_by_cmd(uint32_t        para)   //divid
{
    colorChannal = para;
}



void sm_measure_init(void)
{
    sm7_tick_ms = 0;
    // 不再重置average_times_now，因为它应该在测量序列之间保持
    bilirubin_average = 0;
	tmp102_read_temperature();
    // 重置平均值模式下的显示标志
    extern bool average_results_displayed[3];
    for (int i = 0; i < 3; i++) {
        average_results_displayed[i] = false;
    }
}


void sm_measure_tick(void)
{
    sm7_tick_ms++;

    if (sm7_tick_ms == 1) {

    } else if (sm7_tick_ms == 10) {
//        //启动一次测量
        analog_measure(1);//300ms
        switch_to_next_screen(UI_SCREEN_MEASURE);
    }
}

extern volatile bool stop_flag;
void sm_measure_event(ui_evt_t ui_evt)
{
    //松开探头
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY)
        && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_PRESS)
        && (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_RELEASED)) {
        stop_flag = 1;
    }
    //测量完成
    if (ui_evt.ui_evt_type == UI_EVT_TYPE_MEASURE_DONE) {
        char buffer[128];
        //调用算法
        algo_calc(ui_evt.evt.ui_evt_measure_done.dark, ui_evt.evt.ui_evt_measure_done.green, ui_evt.evt.ui_evt_measure_done.blue,ui_evt.evt.ui_evt_measure_done.tempture);
        // 获取当前测量结果
        uint32_t current_result = get_bilirubin_result();

        // 根据测量模式处理结果
        if (get_measure_mode() == SINGLE_MODE) {
            // 单次测量模式
            single_measurement_result = current_result;
            R = result_show(single_measurement_result, 2);
        } else {
            // 平均值测量模式
            // 存储当前测量结果到对应位置
            if (average_times_now < 3) {
                R = result_show(current_result, 2);
                average_measurement_results[average_times_now] = get_result();
            }

            //求均值
            bilirubin_average += current_result;

            //完成测量
            if ((average_times_now + 1) == average_times_total) {
                bilirubin_average /= average_times_total;
                average_final_result = bilirubin_average;
                R = result_show(average_final_result, 2);
            } else {
                NRF_LOG_INFO("Measurement not complete yet: average_times_now=%d, average_times_total=%d", average_times_now, average_times_total);
            }
        }
		measure_flag = 1;
        sprintf(buffer,"dark: %.2f, green: %.2f, blue: %.2f R: %.2f\r\n",(float)ui_evt.evt.ui_evt_measure_done.dark,(float)ui_evt.evt.ui_evt_measure_done.green,(float)ui_evt.evt.ui_evt_measure_done.blue,R);
        frame_send_string(buffer);
		//保存测量数据
		save_data(0x12345678,get_SOC(),ui_evt.evt.ui_evt_measure_done.dark, ui_evt.evt.ui_evt_measure_done.blue, ui_evt.evt.ui_evt_measure_done.green, get_temperature(),(uint32_t)(R * 1000),GetTimeStamp());
    }
}

//获取结果
float get_result(void)
{
    return R;
}

float result_show(uint32_t result, uint32_t point_num)
{
    if (result > 99999) {
        result = 99999;
    }

    if (point_num != 1 && point_num != 2) {
        return -1.0f;  // 表示非法输入
    }

    // 四舍五入处理
    if (point_num == 1) {
        // 先将结果缩小到合适的范围
        uint32_t temp = result / 10;
        if ((result % 10) >= 5) {
            temp++;
        }
        result = temp * 10;
    } else { // point_num == 2
        // 先将结果缩小到合适的范围
        uint32_t temp = result / 100;
        uint32_t remainder = result % 100;
        if ((remainder % 10) >= 5) {
            if (remainder + 10 >= 100) {
                temp++;
                remainder = 0;
            } else {
                remainder += 10;
            }
        }
        result = temp * 100 + remainder;
    }

    // 确保结果不超过99999
    if (result > 99999) {
        result = 99999;
    }

    // 结果转换为浮点数，根据小数位数缩放
    float value = (float)result / 1000.0f;

    if (point_num == 1) {
        value = ((float)((int)(value * 10 + 0.5f))) / 10.0f;  // 保留1位小数
    } else {
        value = ((float)((int)(value * 100 + 0.5f))) / 100.0f; // 保留2位小数
    }

    return value;
}

